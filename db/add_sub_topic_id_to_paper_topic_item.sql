-- 为paper_topic_item表添加sub_topic_id字段的数据库迁移脚本
-- 创建时间: 2025-01-29
-- 作者: AI Assistant
-- 描述: 为量卷维护-题目-指标选项表添加子题目ID字段，支持题目层级关系

-- 检查字段是否已存在，如果不存在则添加
SET @sql = '';
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'paper_topic_item' 
  AND COLUMN_NAME = 'sub_topic_id';

-- 如果字段不存在，则添加字段
SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE paper_topic_item ADD COLUMN sub_topic_id VARCHAR(32) NULL COMMENT ''子题目ID'' AFTER reserve3;',
    'SELECT ''字段sub_topic_id已存在，跳过添加'' AS message;'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引以提高查询性能
SET @sql = '';
SELECT COUNT(*) INTO @idx_exists 
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'paper_topic_item' 
  AND INDEX_NAME = 'idx_sub_topic_id';

-- 如果索引不存在，则添加索引
SET @sql = IF(@idx_exists = 0, 
    'ALTER TABLE paper_topic_item ADD INDEX idx_sub_topic_id (sub_topic_id);',
    'SELECT ''索引idx_sub_topic_id已存在，跳过添加'' AS message;'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示表结构确认修改
DESCRIBE paper_topic_item;
